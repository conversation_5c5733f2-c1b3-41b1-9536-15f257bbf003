import os
import markdown2
import logging
from flask import Flask, request, jsonify, render_template, send_from_directory, send_file
from pdf_chatbot_prodotti import ProductChatbot # Importa la classe dal tuo script
from dotenv import load_dotenv

# Import logging system components
from middleware import ConversationMiddleware
from api import register_conversation_api
from database import db_manager
from logging_service import conversation_logger

# Carica le variabili d'ambiente dal file .env
load_dotenv()

# --- Inizializzazione ---
app = Flask(__name__)

# Configurazione del logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    handlers=[
                        logging.FileHandler("chat.log"),
                        logging.StreamHandler()
                    ])

# Inizializza il middleware per il logging delle conversazioni
conversation_middleware = ConversationMiddleware(app)

# Registra le API per le statistiche delle conversazioni
register_conversation_api(app)

# Verifica la connessione al database
if not db_manager.test_connection():
    logging.warning("⚠️ Impossibile connettersi al database MySQL. Il logging delle conversazioni non sarà disponibile.")
else:
    logging.info("✅ Connessione al database MySQL stabilita con successo.")

# Recupera le API keys dalle variabili d'ambiente
jina_api_key = os.getenv("JINA_API_KEY")
gemini_api_key = os.getenv("GEMINI_API_KEY")

# Controlla che le API keys siano state impostate
if not jina_api_key or not gemini_api_key:
    logging.error("ERRORE: Imposta le variabili d'ambiente JINA_API_KEY e GEMINI_API_KEY.")
    exit()

# Recupera il livello di verbosità
verbosity_level = os.getenv("VERBOSITY_LEVEL", 3) # Default a 3 se non impostato

# Recupera il prodotto predefinito (se configurato)
default_product_code = os.getenv("DEFAULT_PRODUCT_CODE")

# Inizializza il chatbot UNA SOLA VOLTA all'avvio dell'applicazione
try:
    logging.info("🤖 Inizializzazione del ProductChatbot in corso...")
    chatbot = ProductChatbot(
        jina_api_key=jina_api_key, 
        gemini_api_key=gemini_api_key,
        verbosity_level=verbosity_level
    )
    logging.info(f"✅ Chatbot inizializzato con successo (Livello Verbosità: {verbosity_level}).")
except Exception as e:
    logging.error(f"❌ Errore fatale durante l'inizializzazione del chatbot: {e}")
    exit()


# --- Routing delle Pagine Web ---

@app.route('/')
def index():
    """Renderizza la pagina HTML principale."""
    return render_template('index.html')

@app.route('/embedded')
def embedded():
    """Renderizza il template per l'integrazione embedded."""
    return render_template('embedded.html')


@app.route('/pdf/<product_code>/<path:filename>')
def serve_file(product_code, filename):
    """Serve un file specifico per un prodotto."""
    file_directory = os.path.join(os.getcwd(), 'pdf', product_code)
    file_path = os.path.join(file_directory, filename)
    return send_file(file_path, mimetype='application/pdf')


# --- API Endpoints ---

@app.route('/prepare', methods=['POST'])
def prepare_documents():
    """
    Endpoint per preparare i documenti di un codice prodotto.
    Riceve: {'product_code': 'CODICE_X'}
    Restituisce: {'success': True/False, 'message': '...'}
    """
    data = request.get_json()
    product_code = data.get('product_code')
    logging.info(f"Richiesta di preparazione per il codice prodotto: {product_code}")

    if not product_code:
        logging.warning("Codice prodotto mancante nella richiesta /prepare.")
        return jsonify({'success': False, 'message': 'Codice prodotto mancante.'}), 400

    try:
        documents_ready = chatbot.prepare_product_documents(product_code)
        if documents_ready:
            logging.info(f"Documenti per '{product_code}' pronti.")
            return jsonify({'success': True, 'message': f"Documenti per '{product_code}' pronti."})
        else:
            logging.warning(f"Nessun documento trovato per '{product_code}'.")
            return jsonify({'success': False, 'message': f"Nessun documento trovato per '{product_code}'."})
    except Exception as e:
        logging.error(f"Errore durante la preparazione dei documenti per '{product_code}': {e}")
        return jsonify({'success': False, 'message': f"Errore durante la preparazione: {e}"}), 500


@app.route('/chat', methods=['POST'])
def chat():
    """
    Endpoint per gestire una richiesta di chat.
    Riceve: {'message': 'domanda...', 'product_code': 'CODICE_X', 'history': [...]}
    Restituisce: {'answer': 'risposta...', 'history': [...]}
    """
    data = request.get_json()
    user_message = data.get('message')
    product_code = data.get('product_code')
    history = data.get('history', [])

    if not user_message or not product_code:
        logging.warning("Messaggio o codice prodotto mancante nella richiesta /chat.")
        return jsonify({'answer': 'Messaggio o codice prodotto mancante.'}), 400

    logging.info(f"[CHAT] Utente ({product_code}): {user_message}")

    try:
        # Ottieni la risposta dal chatbot
        bot_response, updated_history, guardrail_data = chatbot.search_and_answer(
            user_message,
            product_code,
            chat_history=history
        )
        logging.info(f"[CHAT] Bot ({product_code}): {bot_response}")

        # Converte i link in formato Markdown (es. [testo](link)) in tag HTML <a>
        html_response = markdown2.markdown(bot_response)

        # Store guardrail data in Flask's g object for middleware access
        from flask import g
        g.guardrail_data = guardrail_data

        return jsonify({'answer': html_response, 'history': updated_history})
    except Exception as e:
        logging.error(f"Errore durante la gestione della chat per '{product_code}': {e}")
        return jsonify({'answer': f"Si è verificato un errore: {e}"}), 500


@app.route('/clear_chat', methods=['POST'])
def clear_chat():
    """Svuota la cronologia della chat (lato client)."""
    # Questa è un'operazione principalmente lato client, 
    # il server deve solo confermare.
    return jsonify({'success': True, 'message': 'Chat cancellata.'})

@app.route('/restart_chat', methods=['POST'])
def restart_chat():
    """Riavvia la sessione di chat."""
    # Anche questa è un'operazione lato client.
    # Potremmo voler reimpostare qualcosa lato server qui in futuro.
    return jsonify({'success': True, 'message': 'Chat riavviata.'})

@app.route('/config', methods=['GET'])
def get_config():
    """Restituisce la configurazione dell'applicazione."""
    return jsonify({
        'default_product_code': default_product_code
    })


# --- Gestione Shutdown ---

import atexit
import signal
import sys

def shutdown_handler(signum=None, frame=None):
    """Handler per la chiusura graceful dell'applicazione."""
    logging.info("🔄 Chiusura dell'applicazione in corso...")
    try:
        # Shutdown del conversation logger
        conversation_logger.shutdown(timeout=30.0)
        logging.info("✅ Sistema di logging delle conversazioni chiuso correttamente.")
    except Exception as e:
        logging.error(f"❌ Errore durante la chiusura del sistema di logging: {e}")

    # Termina l'applicazione
    if signum is not None:
        logging.info("🛑 Terminazione dell'applicazione...")
        sys.exit(0)

# Registra gli handler per la chiusura
atexit.register(shutdown_handler)
signal.signal(signal.SIGTERM, shutdown_handler)
signal.signal(signal.SIGINT, shutdown_handler)

# --- Esecuzione dell'Applicazione ---

if __name__ == '__main__':
    try:
        logging.info("🚀 Avvio dell'applicazione Flask con sistema di logging delle conversazioni...")
        # Esegui l'app Flask. Accessibile da http://127.0.0.1:5001
        app.run(debug=True, port=5001, use_reloader=False)  # Disabilita il reloader per evitare problemi con i signal handler
    except KeyboardInterrupt:
        logging.info("🛑 Interruzione da tastiera ricevuta.")
        # Non chiamare shutdown_handler qui perché è già gestito dal signal handler
        pass
    except Exception as e:
        logging.error(f"❌ Errore durante l'esecuzione dell'applicazione: {e}")
        shutdown_handler()
        sys.exit(1)